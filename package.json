{"name": "internal-web", "version": "0.1.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "build": "next build", "lint": "next lint --fix", "start": "next start -p 3001", "cy:open": "cypress open", "cy:run": "cypress run --component --record --key", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "dev-next": "next dev -p 3001", "dev": "concurrently \"npm run dev-next\"", "dev-https": "next-dev-https --https --qr --port 4430", "prepare": "husky", "fix": "npm run lint && npx tsc -p tsconfig.json", "local-prod": "pnpm lint && pnpm build && pnpm start"}, "dependencies": {"@kickavenue/ui": "^0.0.0-beta.88", "@hookform/resolvers": "^4.1.3", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "date-fns": "^3.6.0", "jodit-react": "^4.1.2", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "14.2.3", "next-auth": "^4.24.7", "next-dev-https": "^0.13.3", "react": "^18", "react-datetime": "^3.2.0", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.13.0", "react-select": "^5.8.3", "react-select-async-paginate": "^0.7.6", "sass": "^1.77.6", "sharp": "^0.33.5", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@next/eslint-plugin-next": "^14.2.4", "@shopify/eslint-plugin": "^45.0.0", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.15.0", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-tailwindcss": "^3.17.4", "eslint-plugin-unused-imports": "^4.1.3", "husky": "^9.0.11", "postcss": "^8", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.1", "tailwindcss": "^3.4.1", "typescript": "~5.4.0"}}