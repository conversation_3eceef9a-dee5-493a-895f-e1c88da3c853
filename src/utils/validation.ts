export const validateEmail = (email: string): boolean => {
  const re = /\S+@\S+\.\S+/
  return re.test(email)
}

export const validatePassword = (password: string): boolean => {
  return password.length >= 6
}

// Utility function to deep compare two objects
export const isEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true

  if (obj1 == null || obj2 == null) return false

  if (typeof obj1 !== "object" || typeof obj2 !== "object") return false

  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) return false

  for (let key of keys1) {
    if (!keys2.includes(key)) return false

    if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
      if (obj1[key].length !== obj2[key].length) return false
      for (let i = 0; i < obj1[key].length; i++) {
        if (obj1[key][i] !== obj2[key][i]) return false
      }
    } else if (!isEqual(obj1[key], obj2[key])) {
      return false
    }
  }

  return true
}
