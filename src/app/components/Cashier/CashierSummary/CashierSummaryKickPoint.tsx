import { formatPrice } from "@utils/misc"

import CashierSummaryField from "./CashierSummaryField"
import useCalcTotalPurchase from "./hook/useCalcTotalPurchase"

const CashierSummaryKickPoint = () => {
  const { isUseKickPoint, kickPointUsage } = useCalcTotalPurchase()

  if (!isUseKickPoint) return null

  return (
    <CashierSummaryField
      isMinus
      label="Kick Points Usage"
      value={formatPrice({ price: kickPointUsage, fallback: "IDR 0" })}
      valueProps={{ state: "success" }}
    />
  )
}

export default CashierSummaryKickPoint
