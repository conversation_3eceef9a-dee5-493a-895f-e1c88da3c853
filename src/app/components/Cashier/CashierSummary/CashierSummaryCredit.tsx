import { formatPrice } from "@utils/misc"

import CashierSummaryField from "./CashierSummaryField"
import useCalcTotalPurchase from "./hook/useCalcTotalPurchase"

const CashierSummaryCredit = () => {
  const { creditValue, isUseCredit, totalCreditUsage } = useCalcTotalPurchase()

  // Calculate proportional usage for each credit type
  const getProportionalUsage = (creditAmount: number) => {
    if (creditValue.totalCredit === 0) return 0
    return (creditAmount / creditValue.totalCredit) * totalCreditUsage
  }

  const creditList = [
    {
      id: "sellerCredit",
      label: "Seller Credit",
      value: getProportionalUsage(creditValue.sellerCredit),
    },
    {
      id: "kickCredit",
      label: "Kick Credit",
      value: getProportionalUsage(creditValue.kickCredit),
    },
  ]

  if (!isUseCredit) return null

  return (
    <CashierSummaryField
      label="Credit Usage"
      value={formatPrice({
        price: totalCreditUsage,
        currency: "- IDR",
        fallback: "- IDR 0",
      })}
      valueProps={{ state: "success" }}
    >
      {creditList?.map((credit) => (
        <CashierSummaryField
          key={credit.id}
          label={credit.label}
          value={formatPrice({
            price: credit.value,
            currency: "- IDR",
            fallback: "- IDR 0",
          })}
          labelProps={{ state: "secondary" }}
          valueProps={{ state: "success" }}
        />
      ))}
    </CashierSummaryField>
  )
}

export default CashierSummaryCredit
