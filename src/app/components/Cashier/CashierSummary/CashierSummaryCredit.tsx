import { formatPrice } from "@utils/misc"

import CashierSummaryField from "./CashierSummaryField"
import useCalcTotalPurchase from "./hook/useCalcTotalPurchase"

const CashierSummaryCredit = () => {
  const { creditValue, isUseCredit } = useCalcTotalPurchase()
  const creditList = [
    {
      id: "sellerCredit",
      label: "Seller Credit",
      value: creditValue.sellerCredit,
    },
    {
      id: "kickCredit",
      label: "Kick Credit",
      value: creditValue.kickCredit,
    },
  ]

  if (!isUseCredit) return null

  return (
    <CashierSummaryField
      label="Credit Usage"
      value={formatPrice({
        price: creditValue.totalCredit,
        currency: "- IDR",
        fallback: "- IDR 0",
      })}
      valueProps={{ state: "success" }}
    >
      {creditList?.map((credit) => (
        <CashierSummaryField
          key={credit.id}
          label={credit.label}
          value={formatPrice({
            price: credit.value,
            currency: "- IDR",
            fallback: "- IDR 0",
          })}
          labelProps={{ state: "secondary" }}
          valueProps={{ state: "success" }}
        />
      ))}
    </CashierSummaryField>
  )
}

export default CashierSummaryCredit
