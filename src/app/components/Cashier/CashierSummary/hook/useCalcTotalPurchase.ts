import { useEffect, useMemo } from "react"
import { useFormContext } from "react-hook-form"

import useFetchMemberBalance from "@app/hooks/useFetchBalance"
import { FormField } from "@constants/formField"
import { calcCreditUsage, getPurchaseValue } from "@utils/cashier.utils"
import { getStripAmount } from "@utils/misc"
import { useCashierFormStore } from "stores/cashierFormStore"

import useSelectedListing from "./useSelectedListing"

const { CREDIT_ENABLED, KICK_POINT_ENABLED } = FormField.CASHIER

const useCalcTotalPurchase = () => {
  const { watch } = useFormContext()
  const { totalPrice: totalItemsPrice } = useSelectedListing()
  const {
    buyerData,
    processingFeeAmount,
    shippingFeeAmount,
    voucherAmount,
    setTotalPurchase,
  } = useCashierFormStore()
  const { data } = useFetchMemberBalance(buyerData?.id)

  const isUseCredit = watch(CREDIT_ENABLED.KEY)
  const isUseKickPoint = watch(KICK_POINT_ENABLED.KEY)

  const kickCreditValue = getStripAmount(data?.content[0]?.kickCredit)
  const sellerCreditValue = getStripAmount(data?.content[0]?.sellerCredit)
  const totalCredit = kickCreditValue + sellerCreditValue
  const kickPointValue = getStripAmount(data?.content[0]?.kickPoint)

  const totalItems = [
    totalItemsPrice,
    getPurchaseValue(processingFeeAmount),
    getPurchaseValue(shippingFeeAmount),
    getPurchaseValue(voucherAmount, true),
  ].reduce((acc, curr) => acc + curr, 0)

  const totalCreditUsage = calcCreditUsage(totalItems, totalCredit)
  const kickPointUsage = calcCreditUsage(totalItems, kickPointValue)

  const totalPurchase: number = useMemo(() => {
    if (isUseCredit && isUseKickPoint) {
      return [
        totalItems,
        getPurchaseValue(totalCreditUsage + kickPointUsage, true),
      ].reduce((acc, curr) => acc + curr, 0)
    }

    if (isUseCredit) {
      return [totalItems, getPurchaseValue(totalCreditUsage, true)].reduce(
        (acc, curr) => acc + curr,
        0,
      )
    }

    if (isUseKickPoint) {
      return [totalItems, getPurchaseValue(kickPointUsage, true)].reduce(
        (acc, curr) => acc + curr,
        0,
      )
    }

    return [totalItems].reduce((acc, curr) => acc + curr, 0)
  }, [
    totalItems,
    isUseCredit,
    totalCreditUsage,
    isUseKickPoint,
    kickPointUsage,
  ])

  useEffect(() => {
    setTotalPurchase(totalPurchase)
  }, [setTotalPurchase, totalPurchase])

  return {
    totalItems,
    totalPurchase,
    totalCreditUsage,
    creditValue: {
      kickCredit: kickCreditValue,
      sellerCredit: sellerCreditValue,
      totalCredit,
    },
    isUseCredit,
    isUseKickPoint,
    kickPointValue,
  }
}

export default useCalcTotalPurchase
