import { useCallback, useEffect, useMemo } from "react"
import { useFormContext } from "react-hook-form"

import useFetchMemberBalance from "@app/hooks/useFetchBalance"
import { FormField } from "@constants/formField"
import { calcCreditUsage, getPurchaseValue } from "@utils/cashier.utils"
import { getStripAmount } from "@utils/misc"
import { useCashierFormStore } from "stores/cashierFormStore"

import useSelectedListing from "./useSelectedListing"

const { CREDIT_ENABLED, KICK_POINT_ENABLED } = FormField.CASHIER

const useCalcTotalPurchase = () => {
  const { watch } = useFormContext()
  const { totalPrice: totalItemsPrice } = useSelectedListing()
  const {
    buyerData,
    processingFeeAmount,
    shippingFeeAmount,
    voucherAmount,
    setTotalPurchase,
  } = useCashierFormStore()
  const { data } = useFetchMemberBalance(buyerData?.id)

  const isUseCredit = watch(CREDIT_ENABLED.KEY)
  const isUseKickPoint = watch(KICK_POINT_ENABLED.KEY)

  // Memoize credit and point values to avoid recalculation
  const creditValues = useMemo(() => {
    const kickCreditValue = getStripAmount(data?.content?.[0]?.kickCredit) || 0
    const sellerCreditValue =
      getStripAmount(data?.content?.[0]?.sellerCredit) || 0
    const totalCredit = kickCreditValue + sellerCreditValue
    const kickPointValue = getStripAmount(data?.content?.[0]?.kickPoint) || 0

    return {
      kickCredit: kickCreditValue,
      sellerCredit: sellerCreditValue,
      totalCredit,
      kickPoint: kickPointValue,
    }
  }, [data?.content])

  // Memoize total items calculation
  const totalItems = useMemo(() => {
    const items = [
      totalItemsPrice || 0,
      getPurchaseValue(processingFeeAmount) || 0,
      getPurchaseValue(shippingFeeAmount) || 0,
      getPurchaseValue(voucherAmount, true) || 0,
    ]

    return items.reduce((acc, curr) => acc + curr, 0)
  }, [totalItemsPrice, processingFeeAmount, shippingFeeAmount, voucherAmount])

  // Memoize credit and kickpoint usage calculations
  const usageCalculations = useMemo(() => {
    const totalCreditUsage = calcCreditUsage(
      totalItems,
      creditValues.totalCredit,
    )
    const kickPointUsage = calcCreditUsage(totalItems, creditValues.kickPoint)

    return {
      totalCreditUsage,
      kickPointUsage,
    }
  }, [totalItems, creditValues.totalCredit, creditValues.kickPoint])

  // Calculate total purchase with proper sequential discount application
  const totalPurchase = useMemo(() => {
    let remainingAmount = totalItems

    // Apply credit discount first if enabled
    if (isUseCredit && usageCalculations.totalCreditUsage > 0) {
      remainingAmount = Math.max(
        0,
        remainingAmount - usageCalculations.totalCreditUsage,
      )
    }

    // Apply kickpoint discount to remaining amount if enabled
    if (isUseKickPoint && usageCalculations.kickPointUsage > 0) {
      // Recalculate kickpoint usage based on remaining amount after credit discount
      const effectiveKickPointUsage = isUseCredit
        ? calcCreditUsage(remainingAmount, creditValues.kickPoint)
        : usageCalculations.kickPointUsage

      remainingAmount = Math.max(0, remainingAmount - effectiveKickPointUsage)
    }

    return Math.max(0, remainingAmount)
  }, [
    totalItems,
    isUseCredit,
    isUseKickPoint,
    usageCalculations.totalCreditUsage,
    usageCalculations.kickPointUsage,
    creditValues.kickPoint,
  ])

  // Memoize the setTotalPurchase callback to prevent unnecessary re-renders
  const updateTotalPurchase = useCallback(() => {
    setTotalPurchase(totalPurchase)
  }, [setTotalPurchase, totalPurchase])

  useEffect(() => {
    updateTotalPurchase()
  }, [updateTotalPurchase])

  return {
    totalItems,
    totalPurchase,
    totalCreditUsage: usageCalculations.totalCreditUsage,
    kickPointUsage: usageCalculations.kickPointUsage,
    creditValue: {
      kickCredit: creditValues.kickCredit,
      sellerCredit: creditValues.sellerCredit,
      totalCredit: creditValues.totalCredit,
    },
    isUseCredit,
    isUseKickPoint,
    kickPointValue: creditValues.kickPoint,
  }
}

export default useCalcTotalPurchase
