import { Label } from "@kickavenue/ui/dist/src/components"
import { Controller, useFormContext } from "react-hook-form"

import SelectVoucher from "@components/shared/Select/SelectVoucher"
import { FormField } from "@constants/formField"
import { VoucherPlatform } from "types/voucher.type"

const { VOUCHER_CODE } = FormField.CASHIER

const CashierPaymentDetailFieldsVoucher = () => {
  const { control } = useFormContext()

  return (
    <div className="flex flex-col gap-sm">
      <Label size="sm" state="default" type="default">
        {VOUCHER_CODE.LABEL}
      </Label>
      <Controller
        name={VOUCHER_CODE.KEY}
        control={control}
        render={({ field }) => (
          <SelectVoucher
            {...field}
            filter={{
              platform: [VoucherPlatform.All, VoucherPlatform.Offline],
            }}
            placeholder={VOUCHER_CODE.PLACEHOLDER}
          />
        )}
      />
    </div>
  )
}

export default CashierPaymentDetailFieldsVoucher
