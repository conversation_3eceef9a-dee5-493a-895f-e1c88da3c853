"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@kickavenue/ui/dist/src/components"
import { useEffect } from "react"
import { FormProvider } from "react-hook-form"

import FormCard from "@components/shared/Form/FormCard"
import ToastAlert from "@components/shared/ToastAlert"
import { useCashierFormStore } from "stores/cashierFormStore"
import { ECashierType } from "types/cashier.type"

import CashierSummaryData from "../CashierSummary/CashierSummaryData"

import {
  AmountReceived,
  Delivery,
  Marketplace,
  PaymentMethod,
  RefNumber,
  Remark,
  Shipping,
  Voucher,
} from "./CashierPaymentDetailFields"
import BuyerEmail from "./CashierPaymentDetailFields/CashierPaymentDetailFieldsBuyerEmail"
import BuyerMobileNumber from "./CashierPaymentDetailFields/CashierPaymentDetailFieldsBuyerMobileNumber"
import BuyerWallet from "./CashierPaymentDetailFields/CashierPaymentDetailFieldsBuyerWallet"
import useCashierForm from "./hooks/useCashierForm"

const CashierPaymentDetailMarketplace = () => {
  const { setECashierType } = useCashierFormStore()
  const { form, onFormValid, onFormInvalid } = useCashierForm()

  useEffect(() => {
    setECashierType(ECashierType.Marketplace)

    return () => {
      setECashierType(null)
    }
  }, [setECashierType])

  return (
    <form onSubmit={form.handleSubmit(onFormValid, onFormInvalid)}>
      <FormProvider {...form}>
        <div className="grid grid-cols-2 gap-[37px]">
          <FormCard className="flex h-fit flex-col gap-lg">
            <Heading heading="4" textStyle="bold">
              Payment Details
            </Heading>

            <Marketplace />
            <BuyerEmail />
            <BuyerMobileNumber />
            <Shipping />
            <Divider orientation="horizontal" />
            <Delivery />
            <PaymentMethod />
            <RefNumber />
            <AmountReceived />
            <BuyerWallet />
            <Voucher />
            <Remark />
          </FormCard>

          <FormCard className="flex h-fit flex-col gap-lg">
            <Heading heading="4" textStyle="bold">
              Purchase Summary
            </Heading>
            <CashierSummaryData />
          </FormCard>
        </div>

        <Button type="submit" size="md" variant="primary">
          Create
        </Button>

        <ToastAlert />
      </FormProvider>
    </form>
  )
}

export default CashierPaymentDetailMarketplace
