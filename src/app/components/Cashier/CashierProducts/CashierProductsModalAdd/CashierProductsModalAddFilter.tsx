"use client"

import { But<PERSON>, DropdownItemProps } from "@kickavenue/ui/dist/src/components"

import useFetchUniqueSize from "@app/hooks/useFetchUniqueSize"
import Dropdown from "@components/shared/Form/Dropdown"
import {
  getHasQuantityOptions,
  getItemConditionOptions,
  getPackagingConditionOptions,
  getShippingOptions,
  getSizeOptions,
} from "@utils/cashier.utils"
import { ESellerListingQuantityType } from "types/sellerListing.type"
import { TUniqueSize } from "types/sizeChart.type"

import useCashierProductsFilter from "../hook/useCashierProductsFilter"

const CashierProductsModalAddFilter = () => {
  const { options: sizeOptions } = useFetchUniqueSize<DropdownItemProps>(
    (size: TUniqueSize) => ({
      text: size.us,
      value: size.id,
    }),
  )

  const {
    draftFilter: filter,
    isDisabled,
    handleItemSelect,
    handleApplyFilter,
    resetFilter,
  } = useCashierProductsFilter()

  return (
    <div className="flex justify-between">
      <div className="flex gap-sm">
        <Dropdown
          value={filter?.shippingMethod}
          defaultSelectedOption={getShippingOptions()[0]}
          options={getShippingOptions(filter?.shippingMethod)}
          className="!w-[173px] border-none"
          onItemSelect={(value) => handleItemSelect("shippingMethod", value)}
          type="button"
        />
        <Dropdown
          value={filter?.sizeId?.join(",")}
          defaultSelectedOption={getSizeOptions(undefined, sizeOptions)[0]}
          options={getSizeOptions(filter?.sizeId, sizeOptions)}
          className="!w-[100px] border-none"
          onItemSelect={(value) => handleItemSelect("sizeId", value)}
          type="button"
        />
        <Dropdown
          value={filter?.itemCondition}
          defaultSelectedOption={getItemConditionOptions()[0]}
          options={getItemConditionOptions(filter?.itemCondition as string)}
          className="!w-[158px] border-none"
          onItemSelect={(value) => handleItemSelect("itemCondition", value)}
          type="button"
        />
        <Dropdown
          value={filter?.packagingCondition}
          defaultSelectedOption={getPackagingConditionOptions()[0]}
          options={getPackagingConditionOptions(
            filter?.packagingCondition as string,
          )}
          onItemSelect={(value) =>
            handleItemSelect("packagingCondition", value)
          }
          className="!w-[193px] border-none"
          type="button"
        />
        <Dropdown
          value={filter?.quantityValue}
          defaultSelectedOption={
            getHasQuantityOptions(ESellerListingQuantityType.HasQuantity)[0]
          }
          options={getHasQuantityOptions(filter?.quantityValue)}
          onItemSelect={(value) => handleItemSelect("hasQuantity", value)}
          className="!w-[130px] border-none"
          type="button"
        />
      </div>

      <div className="flex gap-sm">
        <Button
          variant="secondary"
          size="md"
          className="!px-lg"
          disabled={isDisabled}
          onClick={resetFilter}
        >
          Reset
        </Button>
        <Button
          variant="primary"
          size="md"
          className="!px-lg"
          onClick={handleApplyFilter}
        >
          Apply
        </Button>
      </div>
    </div>
  )
}

export default CashierProductsModalAddFilter
