import { cx } from "class-variance-authority"

import SpinnerLoading from "@components/shared/SpinnerLoading"
import Table from "@components/shared/Table"
import { TableProps } from "types/table.type"

import Empty from "@kickavenue/ui/components/Empty"
import styles from "./CashierProductsModalAddProducts.module.scss"

interface CashierProductsModalAddProductsProps extends TableProps {
  isLoading: boolean
}

const CashierProductsModalAddProducts = ({
  isLoading,
  ...props
}: CashierProductsModalAddProductsProps) => {
  if (isLoading) {
    return <SpinnerLoading />
  }

  if (!props.dataSource.length) {
    return (
      <div className="flex min-h-[50vh] items-center justify-center pb-20">
        <Empty title="No Item Yet" subText="Please select item" />
      </div>
    )
  }

  return <Table className={cx(styles.productsTable, "!mb-24")} {...props} />
}

export default CashierProductsModalAddProducts
