import { isEqual } from "@utils/validation"
import { useCallback, useMemo, useState } from "react"

import { useCashierStore } from "stores/cashierStore"
import {
  ESellerListingQuantityType,
  TSellerListingFilter,
} from "types/sellerListing.type"

const defaultFilter = {
  shippingMethod: "all-shipping-method",
  sizeId: [0],
  itemCondition: "all-item-condition",
  packagingCondition: "all-packaging-condition",
  hasQuantity: true,
  quantityValue: ESellerListingQuantityType.HasQuantity,
  search: "",
}

const useCashierProductsFilter = () => {
  const {
    sellerListing: { filter },
    setSellerListingFilter,
    clearSellerListingFilter,
    clearSellerListing,
  } = useCashierStore()

  const [draftFilter, setDraftFilter] =
    useState<Partial<TSellerListingFilter>>(defaultFilter)

  const handleItemSelect = useCallback(
    (key: keyof TSellerListingFilter, value: string | number[] | boolean) => {
      if (key === "hasQuantity") {
        setDraftFilter({
          ...draftFilter,
          quantityValue: value as ESellerListingQuantityType,
          hasQuantity: value === ESellerListingQuantityType.HasQuantity,
        })
        return
      }

      if (key === "sizeId") {
        console.log("sizeId ", value)

        setDraftFilter({
          ...draftFilter,
          sizeId: (value as string).split(",").map(Number),
        })
        return
      }

      setDraftFilter({
        ...draftFilter,
        [key]: value,
      })
    },
    [draftFilter, setDraftFilter],
  )

  const handleApplyFilter = useCallback(() => {
    setSellerListingFilter(draftFilter as TSellerListingFilter)
  }, [draftFilter, setSellerListingFilter])

  const resetFilter = useCallback(() => {
    clearSellerListingFilter()
    clearSellerListing()
    setDraftFilter(defaultFilter)
    setSellerListingFilter(defaultFilter)
  }, [
    clearSellerListingFilter,
    clearSellerListing,
    setDraftFilter,
    setSellerListingFilter,
  ])

  // Check if current filter or draftFilter is the same as defaultFilter
  const isDisabled = useMemo(() => {
    return isEqual(filter, defaultFilter) || isEqual(draftFilter, defaultFilter)
  }, [filter, draftFilter])

  return {
    filter,
    draftFilter,
    handleItemSelect,
    handleApplyFilter,
    resetFilter,
    isDisabled,
  }
}

export default useCashierProductsFilter
