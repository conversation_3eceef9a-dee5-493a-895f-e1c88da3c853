"use client"

import AsyncCombobox from "@kickavenue/ui/components/AsyncCombobox"
import { forwardRef, useCallback } from "react"
import { ControllerRenderProps } from "react-hook-form"

import { GetAllVoucher } from "@application/usecases/getAllVoucher"
import { VoucherApiRepository } from "@infrastructure/repositories/voucherApiRepository"
import { useSelectVoucherStore } from "stores/selectVoucherStore"
import { TSelectOption } from "types/misc.type"
import { TVoucher, TVoucherFilter } from "types/voucher.type"

interface SelectVoucherProps extends ControllerRenderProps {
  filter?: TVoucherFilter
  optionMapper?: (voucher: TVoucher) => TSelectOption
  placeholder?: string
}

const SelectVoucher = forwardRef<any, SelectVoucherProps>(
  (
    {
      filter: propsFilter,
      placeholder = "Select voucher",
      optionMapper,
      ...rest
    },
    ref,
  ) => {
    const { setVoucherData, setSelectedVoucher } = useSelectVoucherStore()

    const fetchAllVouchers = async (filter?: TVoucherFilter) => {
      const r = new VoucherApiRepository()
      const u = new GetAllVoucher(r)
      const vouchers = await u.execute(filter)
      return vouchers
    }

    const loadOptions = useCallback(
      async (search: string, _: any, additional: any) => {
        const dataPage = additional?.page ?? 0
        const data = await fetchAllVouchers({
          page: dataPage,
          pageSize: 10,
          search,
          ...propsFilter,
        })

        const voucherList = data?.content ?? []

        setVoucherData(voucherList)

        return {
          options: voucherList.map((voucher) =>
            optionMapper
              ? optionMapper(voucher)
              : { value: voucher.code ?? "", label: voucher.code ?? "" },
          ),
          hasMore: !data?.last,
          additional: { page: dataPage + 1 },
        }
      },
      [optionMapper, propsFilter, setVoucherData],
    )

    const handleChange = (value: TSelectOption | null | unknown) => {
      if (value && typeof value === "object" && "value" in value) {
        const selected = useSelectVoucherStore
          .getState()
          .voucherData.find((v) => v.code === value.value)
        setSelectedVoucher(selected ?? null)
      } else {
        setSelectedVoucher(null)
      }

      rest.onChange(value)
    }

    return (
      <AsyncCombobox
        ref={ref}
        {...rest}
        onChange={handleChange}
        additional={{ page: 0 }}
        placeholder={placeholder}
        loadOptions={loadOptions}
        cacheUniqs={[propsFilter]}
        isClearable
      />
    )
  },
)

SelectVoucher.displayName = "SelectVoucher"

export default SelectVoucher
